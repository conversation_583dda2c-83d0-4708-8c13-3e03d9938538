import type { APIRoute } from 'astro';
import { getQRCodeById, storeScanAnalytics } from '../../../database';
import { 
  parseUserAgent, 
  getGeoLocation, 
  getClientIP, 
  generateAnalyticsId,
  isValidRedirectUrl,
  createCorsHeaders,
  parseQRCodeData,
  createErrorResponse
} from '../../../utils';
import type { ScanAnalytics } from '../../../types';

export const GET: APIRoute = async ({ params, request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;
    const db = env?.DB as D1Database;

    if (!db) {
      return createErrorResponse('D1 database not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    const { id } = params;

    if (!id) {
      return createErrorResponse('QR code ID is required', 400, corsHeaders);
    }

    // Get QR code from database
    const qrCode = await getQRCodeById(db, id);

    if (!qrCode) {
      return createErrorResponse('QR code not found', 404, corsHeaders);
    }

    // Parse QR code data to get redirect URL
    let redirectUrl: string;
    try {
      const parsedData = parseQRCodeData(qrCode);
      redirectUrl = parsedData.url;
    } catch (error) {
      console.error('Error parsing QR code data:', error);
      return createErrorResponse('Invalid QR code data', 400, corsHeaders);
    }

    // Validate redirect URL
    if (!isValidRedirectUrl(redirectUrl)) {
      return createErrorResponse('Invalid redirect URL', 400, corsHeaders);
    }

    // Collect analytics data
    const now = new Date().toISOString();
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const referrer = request.headers.get('Referer');
    const deviceInfo = parseUserAgent(userAgent);
    const geoLocation = getGeoLocation(request);

    const analyticsData: ScanAnalytics = {
      id: generateAnalyticsId(),
      qr_code_id: qrCode.id,
      scan_time: now,
      ip: clientIP,
      user_agent: userAgent,
      referrer: referrer,
      lat: geoLocation.lat,
      lon: geoLocation.lon,
      city: geoLocation.city,
      country: geoLocation.country,
      device: deviceInfo.device,
      os: deviceInfo.os,
      browser: deviceInfo.browser,
      created_at: now
    };

    // Store analytics data asynchronously (don't wait for it to complete)
    // Note: In Astro, we can't use ctx.waitUntil like in Workers, so we'll fire and forget
    storeScanAnalytics(db, analyticsData).catch(error => {
      console.error('Failed to store analytics:', error);
    });

    // Perform the redirect
    return Response.redirect(redirectUrl, 302);

  } catch (error) {
    console.error('QR redirect error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
