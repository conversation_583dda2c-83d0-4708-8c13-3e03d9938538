import type { APIRoute } from 'astro';
import { checkDatabaseHealth } from '../../database';
import { createCorsHeaders, createSuccessResponse, createErrorResponse } from '../../utils';

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;
    const db = env?.DB as D1Database;

    if (!db) {
      return createErrorResponse('D1 database not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    // Check database health
    const isHealthy = await checkDatabaseHealth(db);
    
    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: isHealthy
    };

    return createSuccessResponse(
      healthData,
      isHealthy ? 200 : 503,
      corsHeaders
    );

  } catch (error) {
    console.error('Health check error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
